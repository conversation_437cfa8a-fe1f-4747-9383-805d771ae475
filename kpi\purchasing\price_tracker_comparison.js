// Price Tracker Comparison Module - Competitive Intelligence
export class PriceTrackerComparison {
  constructor(priceData, currencyConversion) {
    this.priceData = priceData || [];
    this.currencyConversion = currencyConversion;
    this.modal = null;
    this.currentView = 'vendor-comparison'; // vendor-comparison, market-share, price-elasticity
  }

  show() {
    this.createModal();
    this.render();
  }

  createModal() {
    // Remove existing modal if any
    const existingModal = document.getElementById('comparison-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Create modal overlay
    this.modal = document.createElement('div');
    this.modal.id = 'comparison-modal';
    this.modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    
    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden';
    
    this.modal.appendChild(modalContainer);
    document.body.appendChild(this.modal);

    // Setup close handlers
    this.setupCloseHandlers();
  }

  setupCloseHandlers() {
    // Close on overlay click
    this.modal.addEventListener('click', (e) => {
      if (e.target === this.modal) {
        this.close();
      }
    });

    // Close on escape key
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        this.close();
        document.removeEventListener('keydown', handleKeyDown);
      }
    };
    document.addEventListener('keydown', handleKeyDown);
  }

  close() {
    if (this.modal && this.modal.parentNode) {
      this.modal.parentNode.removeChild(this.modal);
    }
  }

  render() {
    const container = this.modal.querySelector('div');
    container.innerHTML = `
      <div class="flex flex-col h-full">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center gap-4">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Competitive Intelligence</h2>
            <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button class="tab-btn px-3 py-1 text-sm rounded-md transition-colors ${this.currentView === 'vendor-comparison' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'}" data-view="vendor-comparison">
                Multi-Vendor
              </button>
              <button class="tab-btn px-3 py-1 text-sm rounded-md transition-colors ${this.currentView === 'market-share' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'}" data-view="market-share">
                Market Share
              </button>
              <button class="tab-btn px-3 py-1 text-sm rounded-md transition-colors ${this.currentView === 'price-elasticity' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'}" data-view="price-elasticity">
                Price Elasticity
              </button>
            </div>
          </div>
          <button class="close-btn text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div class="flex-1 overflow-auto p-6">
          <div id="comparison-content">
            ${this.renderCurrentView()}
          </div>
        </div>
      </div>
    `;

    this.setupEventListeners();
  }

  renderCurrentView() {
    switch (this.currentView) {
      case 'vendor-comparison':
        return this.renderVendorComparison();
      case 'market-share':
        return this.renderMarketShare();
      case 'price-elasticity':
        return this.renderPriceElasticity();
      default:
        return this.renderVendorComparison();
    }
  }

  renderVendorComparison() {
    const vendorData = this.analyzeVendorPricing();
    
    return `
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Multi-Vendor Price Comparison</h3>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Comparing ${vendorData.length} vendors across ${this.getUniquePartsCount()} parts
          </div>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Part Number</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Best Price</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Vendor Comparison</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Savings Opportunity</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderVendorComparisonRows(vendorData)}
            </tbody>
          </table>
        </div>
      </div>
    `;
  }

  renderMarketShare() {
    const marketData = this.analyzeMarketShare();
    
    return `
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Market Share Analysis</h3>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Based on purchase volume and frequency
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Market Share Chart -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Vendor Market Share</h4>
            <div class="space-y-3">
              ${marketData.map(vendor => `
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-4 h-4 rounded-full mr-3" style="background-color: ${vendor.color}"></div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">${vendor.name}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div class="h-2 rounded-full" style="width: ${vendor.percentage}%; background-color: ${vendor.color}"></div>
                    </div>
                    <span class="text-sm text-gray-600 dark:text-gray-400 w-12 text-right">${vendor.percentage}%</span>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>

          <!-- Category Dominance -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Category Dominance</h4>
            <div class="space-y-3">
              ${this.renderCategoryDominance()}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderPriceElasticity() {
    const elasticityData = this.analyzePriceElasticity();
    
    return `
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Price Elasticity Analysis</h3>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            How demand responds to price changes
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          ${elasticityData.map(item => `
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <h4 class="text-md font-medium text-gray-900 dark:text-white">${item.partNumber}</h4>
                <span class="text-xs px-2 py-1 rounded-full ${item.elasticity === 'High' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : item.elasticity === 'Medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'}">
                  ${item.elasticity} Elasticity
                </span>
              </div>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600 dark:text-gray-400">Price Range:</span>
                  <span class="text-gray-900 dark:text-white">${item.priceRange}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600 dark:text-gray-400">Volume Impact:</span>
                  <span class="text-gray-900 dark:text-white">${item.volumeImpact}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600 dark:text-gray-400">Recommendation:</span>
                  <span class="text-gray-900 dark:text-white">${item.recommendation}</span>
                </div>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Tab switching
    const tabBtns = this.modal.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        this.currentView = btn.getAttribute('data-view');
        this.render();
      });
    });

    // Close button
    const closeBtn = this.modal.querySelector('.close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.close();
      });
    }
  }

  // Analysis methods
  analyzeVendorPricing() {
    const partVendorMap = new Map();

    // Group by part number and collect vendor prices
    this.priceData.forEach(item => {
      const partId = item.inventoryId?.trim();
      if (!partId) return;

      if (!partVendorMap.has(partId)) {
        partVendorMap.set(partId, new Map());
      }

      const vendorMap = partVendorMap.get(partId);
      const vendorName = item.vendorName;
      const price = this.convertPrice(item.unitCost, item.currency);

      if (!vendorMap.has(vendorName)) {
        vendorMap.set(vendorName, []);
      }
      vendorMap.get(vendorName).push(price);
    });

    // Calculate average prices and find best deals
    const results = [];
    partVendorMap.forEach((vendorMap, partId) => {
      const vendors = [];
      let bestPrice = Infinity;
      let bestVendor = '';

      vendorMap.forEach((prices, vendorName) => {
        const avgPrice = prices.reduce((sum, p) => sum + p, 0) / prices.length;
        vendors.push({ name: vendorName, price: avgPrice, count: prices.length });

        if (avgPrice < bestPrice) {
          bestPrice = avgPrice;
          bestVendor = vendorName;
        }
      });

      if (vendors.length > 1) { // Only include parts with multiple vendors
        results.push({
          partId,
          vendors: vendors.sort((a, b) => a.price - b.price),
          bestPrice,
          bestVendor,
          savingsOpportunity: this.calculateSavingsOpportunity(vendors)
        });
      }
    });

    return results.sort((a, b) => b.savingsOpportunity.amount - a.savingsOpportunity.amount);
  }

  analyzeMarketShare() {
    const vendorStats = new Map();
    let totalTransactions = 0;

    this.priceData.forEach(item => {
      const vendor = item.vendorName;
      if (!vendorStats.has(vendor)) {
        vendorStats.set(vendor, { count: 0, totalValue: 0 });
      }

      const stats = vendorStats.get(vendor);
      stats.count++;
      stats.totalValue += this.convertPrice(item.unitCost, item.currency);
      totalTransactions++;
    });

    const colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#6B7280'];
    let colorIndex = 0;

    const results = Array.from(vendorStats.entries())
      .map(([name, stats]) => ({
        name,
        count: stats.count,
        totalValue: stats.totalValue,
        percentage: Math.round((stats.count / totalTransactions) * 100),
        color: colors[colorIndex++ % colors.length]
      }))
      .sort((a, b) => b.percentage - a.percentage);

    return results;
  }

  analyzePriceElasticity() {
    const partAnalysis = new Map();

    // Group by part and analyze price variations
    this.priceData.forEach(item => {
      const partId = item.inventoryId?.trim();
      if (!partId) return;

      if (!partAnalysis.has(partId)) {
        partAnalysis.set(partId, []);
      }

      partAnalysis.get(partId).push({
        price: this.convertPrice(item.unitCost, item.currency),
        date: item.purchaseDate,
        vendor: item.vendorName
      });
    });

    const results = [];
    partAnalysis.forEach((data, partId) => {
      if (data.length < 3) return; // Need enough data points

      const prices = data.map(d => d.price).sort((a, b) => a - b);
      const minPrice = prices[0];
      const maxPrice = prices[prices.length - 1];
      const avgPrice = prices.reduce((sum, p) => sum + p, 0) / prices.length;
      const priceVariation = ((maxPrice - minPrice) / avgPrice) * 100;

      let elasticity, recommendation;
      if (priceVariation > 30) {
        elasticity = 'High';
        recommendation = 'Negotiate aggressively';
      } else if (priceVariation > 15) {
        elasticity = 'Medium';
        recommendation = 'Monitor closely';
      } else {
        elasticity = 'Low';
        recommendation = 'Stable pricing';
      }

      results.push({
        partNumber: partId,
        priceRange: `$${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)}`,
        volumeImpact: `${data.length} purchases`,
        elasticity,
        recommendation,
        variation: priceVariation
      });
    });

    return results.sort((a, b) => b.variation - a.variation).slice(0, 9); // Top 9 for grid layout
  }

  renderVendorComparisonRows(vendorData) {
    return vendorData.slice(0, 10).map(item => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
          ${item.partId}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
          <div class="flex items-center">
            <span class="font-medium">$${item.bestPrice.toFixed(2)}</span>
            <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">${item.bestVendor}</span>
          </div>
        </td>
        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
          <div class="space-y-1">
            ${item.vendors.slice(0, 3).map((vendor, index) => `
              <div class="flex items-center justify-between">
                <span class="text-xs ${index === 0 ? 'font-medium text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'}">${vendor.name}</span>
                <span class="text-xs ${index === 0 ? 'font-medium text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'}">$${vendor.price.toFixed(2)}</span>
              </div>
            `).join('')}
            ${item.vendors.length > 3 ? `<div class="text-xs text-gray-400">+${item.vendors.length - 3} more</div>` : ''}
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm">
          <div class="flex items-center">
            <span class="font-medium text-green-600 dark:text-green-400">$${item.savingsOpportunity.amount.toFixed(2)}</span>
            <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">(${item.savingsOpportunity.percentage}%)</span>
          </div>
        </td>
      </tr>
    `).join('');
  }

  renderCategoryDominance() {
    const marketData = this.analyzeMarketShare();
    return marketData.slice(0, 5).map(vendor => `
      <div class="flex items-center justify-between text-sm">
        <span class="text-gray-900 dark:text-white">${vendor.name}</span>
        <span class="text-gray-600 dark:text-gray-400">${vendor.count} parts</span>
      </div>
    `).join('');
  }

  calculateSavingsOpportunity(vendors) {
    if (vendors.length < 2) return { amount: 0, percentage: 0 };

    const sortedVendors = vendors.sort((a, b) => a.price - b.price);
    const bestPrice = sortedVendors[0].price;
    const avgPrice = vendors.reduce((sum, v) => sum + v.price, 0) / vendors.length;

    const savings = avgPrice - bestPrice;
    const percentage = Math.round((savings / avgPrice) * 100);

    return { amount: savings, percentage };
  }

  convertPrice(price, currency) {
    const numPrice = parseFloat(price);
    if (this.currencyConversion?.enabled && currency === 'USD' && this.currencyConversion.toCurrency === 'CAD') {
      return numPrice * this.currencyConversion.rate;
    }
    return numPrice;
  }

  getUniquePartsCount() {
    const uniqueParts = new Set(this.priceData.map(item => item.inventoryId?.trim()).filter(Boolean));
    return uniqueParts.size;
  }
}
