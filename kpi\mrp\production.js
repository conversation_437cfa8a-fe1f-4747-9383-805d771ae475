// Production component for MRP Dashboard
import { connectionManager } from '../../core/connection.js';

export class ProductionComponent {
  constructor(container) {
    this.container = container;
    this.productionOrders = [];
    this.filteredOrders = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'MainOrderDate';  // Default sort by date
    this.sortDirection = 'desc';       // Default newest first
    this.filterStatus = 'all';
    this.isLoading = true;
    this.dbName = 'productionDb';
    this.storeName = 'productionOrders';
    this.settingsStoreName = 'appSettings';
    this.dataSource = null; // 'acumatica', 'indexedDB'
    this.lastSyncTime = null;
    
    // Default date range - last 3 months to now
    const now = new Date();
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(now.getMonth() - 3);
    
    this.dateRange = {
      start: threeMonthsAgo,
      end: now
    };
  }

  async init() {
    console.log("Initializing Production component");
    
    // Only use a single loading indicator
    this.isLoading = true;
    this.render();
    
    try {
      // Initialize IndexedDB
      await this.initDatabase();
      
      // Check Acumatica connection status
      const connectionStatus = connectionManager.getConnectionStatus();
      const isConnected = connectionStatus && connectionStatus.acumatica && connectionStatus.acumatica.isConnected;
      
      // Load production order data - fetch from Acumatica if connected
      await this.loadData(isConnected);
    
      // Update loading state and render again
      this.isLoading = false;
      this.render();
    
      // Set up event listeners
      this.setupEventListeners();
    } catch (error) {
      console.error("Error initializing production orders:", error);
      this.isLoading = false;
      this.showError("Failed to initialize: " + error.message);
      this.render();
    }
  }

  async initDatabase() {
    return new Promise((resolve, reject) => {
      // Try to open the database without specifying version
      const checkRequest = indexedDB.open(this.dbName);
      
      checkRequest.onsuccess = (event) => {
        const db = event.target.result;
        let needsUpgrade = false;
        
        // Check if all required stores exist
        if (!db.objectStoreNames.contains(this.storeName)) {
          needsUpgrade = true;
        }
        if (!db.objectStoreNames.contains(this.settingsStoreName)) {
          needsUpgrade = true;
        }
        
        const currentVersion = db.version;
        db.close();
        
        if (!needsUpgrade) {
          // Database exists and has all needed stores
          console.log("Production database already exists with correct schema, version:", currentVersion);
          resolve();
          return;
        }
        
        // Need to upgrade the database
        console.log("Need to upgrade production database schema");
        
        // Open with a new version number
        const request = indexedDB.open(this.dbName, currentVersion + 1);
        
        request.onerror = (event) => {
          console.error("Error opening IndexedDB:", event.target.error);
          reject(new Error("Could not open production database"));
        };
        
        request.onsuccess = (event) => {
          console.log("Successfully opened production database");
          resolve();
        };
        
        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          console.log("Upgrading production database schema to version", db.version);
          
          // Create object store for production orders if it doesn't exist
          if (!db.objectStoreNames.contains(this.storeName)) {
            const store = db.createObjectStore(this.storeName, { keyPath: "id" });
            
            // Create indices with unique: false
            store.createIndex("MainProductionNbr", "MainProductionNbr", { unique: false });
            store.createIndex("MainOrderDate", "MainOrderDate", { unique: false });
            store.createIndex("MainInventoryID", "MainInventoryID", { unique: false });
            store.createIndex("MainStatus", "MainStatus", { unique: false });
            store.createIndex("MainOrderType", "MainOrderType", { unique: false });
            
            console.log("Created production orders store");
          }
          
          // Create object store for app settings if it doesn't exist
          if (!db.objectStoreNames.contains(this.settingsStoreName)) {
            db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
            console.log("Created settings store");
          }
          
          console.log("Production database schema upgrade complete");
        };
      };
      
      checkRequest.onerror = (event) => {
        console.error("Error checking database:", event.target.error);
        
        // If we can't open the database at all, try creating it from scratch
        const freshRequest = indexedDB.open(this.dbName, 1);
        
        freshRequest.onerror = (event) => {
          console.error("Error creating new database:", event.target.error);
          reject(new Error("Could not create production database"));
        };
        
        freshRequest.onsuccess = (event) => {
          console.log("Successfully created fresh production database");
          resolve();
        };
        
        freshRequest.onupgradeneeded = (event) => {
          const db = event.target.result;
          console.log("Creating new production database schema");
          
          // Create object store for production orders
          const store = db.createObjectStore(this.storeName, { keyPath: "id" });
          
          // Create indices
          store.createIndex("MainProductionNbr", "MainProductionNbr", { unique: false });
          store.createIndex("MainOrderDate", "MainOrderDate", { unique: false });
          store.createIndex("MainInventoryID", "MainInventoryID", { unique: false });
          store.createIndex("MainStatus", "MainStatus", { unique: false });
          store.createIndex("MainOrderType", "MainOrderType", { unique: false });
          
          // Create object store for app settings
          db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
          
          console.log("Production database schema created");
        };
      };
    });
  }

  async loadData(forceRefresh = false) {
    try {
      this.isLoading = true;
      this.render();
      
      // Check connection status
      const connectionStatus = connectionManager.getConnectionStatus();
      console.log("Acumatica connection status:", connectionStatus.acumatica);
      
      // When forceRefresh is true, always try to fetch from Acumatica first if connected
      if (forceRefresh && connectionStatus.acumatica.isConnected) {
        console.log("Force refreshing - fetching latest production data from Acumatica");
        try {
          const result = await this.fetchAcumaticaProduction(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            console.log("Successfully fetched data from Acumatica");
            this.productionOrders = this.parseAcumaticaProduction(result.data);
            console.log(`Parsed ${this.productionOrders.length} production orders`);
            
            // Verify operations and materials are properly parsed
            let totalOperations = 0;
            let totalMaterials = 0;
            this.productionOrders.forEach(order => {
              if (Array.isArray(order.Operations)) {
                totalOperations += order.Operations.length;
              }
              if (Array.isArray(order.Materials)) {
                totalMaterials += order.Materials.length;
              }
            });
            console.log(`Total operations: ${totalOperations}, total materials: ${totalMaterials}`);
            
            // Update data source and sync time
            this.dataSource = 'acumatica';
            this.lastSyncTime = new Date().toLocaleString();
            
            // Store in IndexedDB for offline access
            await this.storeProductionInIndexedDB(this.productionOrders);
            
            // Also store the sync info in settings
            await this.saveSettings({
              lastSyncTime: this.lastSyncTime,
              dateRange: {
                start: this.dateRange.start.toISOString(),
                end: this.dateRange.end.toISOString()
              }
            });
            
            console.log(`Stored ${this.productionOrders.length} production orders in IndexedDB`);
            
            // Continue with filtering
            this.filteredOrders = [...this.productionOrders];
            this.calculateTotalPages();
            this.isLoading = false;
            this.render();
            return;
          } else {
            console.warn("Error refreshing from Acumatica:", result.error);
            // Fall through to IndexedDB fallback
          }
        } catch (fetchError) {
          console.error("Error refreshing production from Acumatica:", fetchError);
          // Fall through to IndexedDB fallback
        }
      }
      
      // Try to get data from IndexedDB first
      console.log("Attempting to fetch data from IndexedDB...");
      this.productionOrders = await this.getProductionFromIndexedDB();
      
      // Verify operations and materials in retrieved data
      if (this.productionOrders.length > 0) {
        let totalOperations = 0;
        let totalMaterials = 0;
        this.productionOrders.forEach(order => {
          if (Array.isArray(order.Operations)) {
            totalOperations += order.Operations.length;
          }
          if (Array.isArray(order.Materials)) {
            totalMaterials += order.Materials.length;
          }
        });
        console.log(`Retrieved from IndexedDB - Total operations: ${totalOperations}, total materials: ${totalMaterials}`);
      }
      
      // Also try to get the last sync time and date range
      const settings = await this.loadSettings();
      if (settings) {
        if (settings.lastSyncTime) {
          this.lastSyncTime = settings.lastSyncTime;
        }
        if (settings.dateRange) {
          this.dateRange = {
            start: new Date(settings.dateRange.start),
            end: new Date(settings.dateRange.end)
          };
        }
      }
      
      // If we have data in IndexedDB, use it
      if (this.productionOrders.length > 0) {
        console.log(`Retrieved ${this.productionOrders.length} production orders from IndexedDB`);
        this.dataSource = 'indexedDB';
        this.filteredOrders = [...this.productionOrders];
        this.calculateTotalPages();
        this.isLoading = false;
        this.render();
        return;
      }
      
      // If no data in IndexedDB and connected to Acumatica, try fetching from there
      if (connectionStatus.acumatica.isConnected && !forceRefresh) {
        console.log("No data in IndexedDB, trying to fetch from Acumatica");
        try {
          const result = await this.fetchAcumaticaProduction(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            this.productionOrders = this.parseAcumaticaProduction(result.data);
            
            // Update data source and sync time
            this.dataSource = 'acumatica';
            this.lastSyncTime = new Date().toLocaleString();
            
            await this.storeProductionInIndexedDB(this.productionOrders);
            
            // Also store the sync info in settings
            await this.saveSettings({
              lastSyncTime: this.lastSyncTime,
              dateRange: {
                start: this.dateRange.start.toISOString(),
                end: this.dateRange.end.toISOString()
              }
            });
            
            console.log(`Stored ${this.productionOrders.length} production orders in IndexedDB`);
            this.filteredOrders = [...this.productionOrders];
            this.calculateTotalPages();
            this.isLoading = false;
            this.render();
            return;
          } else {
            console.warn("Error fetching from Acumatica:", result.error);
          }
        } catch (fetchError) {
          console.error("Error fetching production from Acumatica:", fetchError);
        }
      }
      
      // If we reach here, we couldn't get data from either Acumatica or IndexedDB
      this.productionOrders = [];
      this.filteredOrders = [];
      this.dataSource = 'empty';
      this.calculateTotalPages();
      this.isLoading = false;
      this.render();
      this.showError("No production data available. Please connect to Acumatica to fetch data.");
      
    } catch (error) {
      console.error('Error loading production data:', error);
      this.productionOrders = [];
      this.filteredOrders = [];
      this.dataSource = 'empty';
      this.calculateTotalPages();
      this.isLoading = false;
      this.render();
      this.showError("Error loading production data: " + error.message);
    }
  }

  async fetchAcumaticaProduction(instance) {
    try {
      if (!instance) {
        const connectionStatus = connectionManager.getConnectionStatus();
        if (!connectionStatus.acumatica.instance) {
          throw new Error('No Acumatica instance URL available. Please check connection.');
        }
        instance = connectionStatus.acumatica.instance;
      }
      
      // Use the specific URL with the exact structure required
      // Just change the dates in the URL but keep everything else exactly the same
      let startDateStr = '2025-01-01';
      let endDateStr = '2025-05-01';
      
      // Only modify the dates if we have valid date ranges set
      if (this.dateRange && this.dateRange.start && this.dateRange.end) {
        // Format dates as YYYY-MM-DD
        const formatDate = (date) => {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          return `${year}-${month}-${day}`;
        };
        
        startDateStr = formatDate(this.dateRange.start);
        endDateStr = formatDate(this.dateRange.end);
      }
      
      // Keep the exact same URL format, just swap in our dates
      const apiUrl = `${instance}/entity/Enventbridge/22.200.001/ProductionOrder?$filter=MainOrderDate ge datetimeoffset'${startDateStr}T00:00:00Z' and MainOrderDate lt datetimeoffset'${endDateStr}T00:00:00Z'&$expand=Opreations/Materials`;
      
      console.log("Fetching production orders with exact URL:", apiUrl);
      
      // The rest of the method stays exactly the same
      let cookieString = '';
      if (chrome?.cookies?.getAll) {
        try {
          const url = new URL(instance);
          const cookies = await chrome.cookies.getAll({ domain: url.hostname });
          cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
          console.log("Retrieved cookies for authentication");
        } catch (cookieError) {
          console.warn("Could not retrieve cookies:", cookieError);
        }
      }
      
      // Make request with cookies through the connection manager
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...(cookieString ? { 'Cookie': cookieString } : {})
        },
        credentials: 'include'  // Include cookies for authentication
      });
      
      // Log the response status for debugging
      console.log("Acumatica API response status:", response.status);
      
      // Check response
      if (!response.ok) {
        if (response.status === 401) {
          // Update connection status in connection manager
          connectionManager.connections.acumatica.isConnected = false;
          if (chrome?.storage?.local) {
            await chrome.storage.local.set({ 'connections': connectionManager.connections });
          }
          throw new Error('Authentication failed. Please reconnect to Acumatica.');
        }
        
        // Get response text for better error diagnostics
        const responseText = await response.text();
        throw new Error(`Failed to fetch production orders: ${response.status} ${response.statusText}. Details: ${responseText.substring(0, 200)}`);
      }
      
      // Parse response
      const data = await response.json();
      console.log(`Received ${data.length} production orders from Acumatica API`);
      
      // Validate the data structure has the expected format
      if (data.length > 0) {
        const firstItem = data[0];
        console.log("First item structure check:", {
          hasMainProductionNbr: !!firstItem.MainProductionNbr,
          hasOperations: !!firstItem.Opreations,
          operationsCount: firstItem.Opreations ? firstItem.Opreations.length : 0
        });
        
        // Log the full first item to debug (limiting nested objects for readability)
        console.log("First item sample:", this.simplifyForLogging(firstItem));
        
        // Log the first operation if available
        if (firstItem.Opreations && firstItem.Opreations.length > 0) {
          const firstOp = firstItem.Opreations[0];
          console.log("First operation:", {
            id: firstOp.id,
            OperationID: firstOp.OperationID?.value,
            hasMaterials: !!firstOp.Materials,
            materialsCount: firstOp.Materials ? firstOp.Materials.length : 0
          });
          
          // Log the first material if available
          if (firstOp.Materials && firstOp.Materials.length > 0) {
            console.log("First material:", {
              id: firstOp.Materials[0].id,
              InventoryID: firstOp.Materials[0].InventoryID?.value,
              QtyRequired: firstOp.Materials[0].QtyRequired?.value
            });
          }
        }
      }
      
      return { success: true, data };
    } catch (error) {
      console.error("Error fetching production from Acumatica:", error);
      return { success: false, error: error.message };
    }
  }
  
  // Helper method to simplify objects for logging
  simplifyForLogging(obj, depth = 0) {
    if (depth > 1) return "[Nested Object]";
    if (!obj || typeof obj !== 'object') return obj;
    
    const result = {};
    for (const key in obj) {
      if (Array.isArray(obj[key])) {
        result[key] = `[Array: ${obj[key].length} items]`;
        if (obj[key].length > 0) {
          result[`${key}[0]`] = this.simplifyForLogging(obj[key][0], depth + 1);
        }
      } else if (typeof obj[key] === 'object') {
        if (key === 'value') {
          result[key] = obj[key];
        } else {
          result[key] = this.simplifyForLogging(obj[key], depth + 1);
        }
      } else {
        result[key] = obj[key];
      }
    }
    return result;
  }

  parseAcumaticaProduction(productionData) {
    try {
      console.log("Parsing Acumatica production data, raw sample:", this.simplifyForLogging(productionData[0]));
      
      // Standard Enventbridge format processing - directly matches the API response structure
      return productionData.map(order => {
        try {
          console.log("Processing order:", order.MainProductionNbr?.value);
          
          // Generate unique ID if needed
          const id = order.id || `prod-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
          
          // Extract main production order fields from the sample structure
          const mainProductionNbr = order.MainProductionNbr?.value || '';
          const mainOrderType = order.MainOrderType?.value || '';
          const mainOrderTypeDescription = order.MainOrderTypeDescription?.value || '';
          const mainStatus = order.MainStatus?.value || '';
          const mainDescription = order.MainInventoryIDDescription?.value || '';
          const mainInventoryID = order.MainInventoryID?.value || '';
          const mainInventoryClass = order.MainInventoryIDItemClass?.value || '';
          const mainUOM = order.MainInventoryIDBaseUnit?.value || '';
          const mainWarehouse = order.MainWarehouse?.value || '';
          
          // Parse dates - completely avoid Date objects for API dates
          const parseDate = dateStr => {
            if (!dateStr) return null;
            try {
              // Just store the raw string value without creating a Date object
              // This avoids any timezone conversion issues
              const rawDateString = dateStr.value || dateStr;
              
              // For sorting and other operations, extract date parts directly from string
              let year, month, day;
              
              if (typeof rawDateString === 'string' && rawDateString.includes('T')) {
                // Extract date parts from ISO format (2025-04-29T00:00:00+00:00)
                const datePart = rawDateString.split('T')[0];
                [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
              }
              
              return {
                // Keep the raw string exactly as it came from API
                rawValue: rawDateString,
                // Store date parts for direct access
                year: year,
                month: month,
                day: day,
                // Identifiable type
                isAcumaticaDate: true
              };
            } catch (e) {
              console.error("Error parsing date:", dateStr, e);
              return null;
            }
          };
          
          const mainOrderDate = parseDate(order.MainOrderDate?.value);
          
          // Process operations - Correctly using "Opreations" as spelled in the API
          const operations = [];
          
          // DEBUG: Log operations data
          console.log("Operations data for", mainProductionNbr, ":", order.Opreations ? `${order.Opreations.length} operations found` : "none");
          
          if (Array.isArray(order.Opreations) && order.Opreations.length > 0) {
            order.Opreations.forEach(op => {
              // Create the operation object
              const operation = {
                id: op.id || `op-${id}-${Math.random().toString(36).substring(2, 10)}`,
                OperationNbr: op.OperationID?.value || '',
                OperationDescription: op.OperationDescription?.value || '',
                WorkCenter: op.WorkCenter?.value || '',
                Status: mainStatus || '', // Use main status since operation status isn't provided
                StartDate: parseDate(op.StartDate?.value),
                EndDate: null,
                LaborTime: 0,
                MachineTime: 0
              };
              
              operations.push(operation);
              console.log(`Added operation ${operation.OperationNbr} to ${mainProductionNbr}`);
            });
          }
          
          console.log(`Processed ${operations.length} operations for ${mainProductionNbr}`);
          
          // Process all materials across all operations
          const materials = [];
          let totalMaterialCost = 0;
          
          if (Array.isArray(order.Opreations)) {
            order.Opreations.forEach(op => {
              const opId = op.OperationID?.value || '';
              
              // DEBUG: Log materials data
              console.log(`Materials for operation ${opId}:`, op.Materials ? `${op.Materials.length} materials found` : "none");
              
              if (Array.isArray(op.Materials) && op.Materials.length > 0) {
                op.Materials.forEach(mat => {
                  const qtyRequired = parseFloat(mat.QtyRequired?.value || 0);
                  const unitCost = parseFloat(mat.UnitCost?.value || 0);
                  
                  // Calculate total cost for this material
                  const totalCost = qtyRequired * unitCost;
                  totalMaterialCost += totalCost;
                  
                  // Create the material object
                  const material = {
                    id: mat.id || `mat-${id}-${Math.random().toString(36).substring(2, 10)}`,
                    InventoryID: mat.InventoryID?.value || '',
                    Description: mat.Description?.value || '',
                    Warehouse: order.MainWarehouse?.value || '',
                    OperationNbr: opId,
                    UOM: mat.UOM?.value || '',
                    QtyRequired: qtyRequired,
                    QtyIssued: qtyRequired, // For completed orders, assume issued = required
                    UnitCost: unitCost,
                    TotalCost: totalCost
                  };
                  
                  materials.push(material);
                  console.log(`Added material ${material.InventoryID} to operation ${opId} for ${mainProductionNbr}`);
                });
              }
            });
          }
          
          console.log(`Processed ${materials.length} materials with total cost ${totalMaterialCost} for ${mainProductionNbr}`);
          
          // For completed orders, assume quantities are fulfilled
          const isCompleted = mainStatus.toLowerCase() === 'completed';
          const totalQtyRequired = materials.reduce((sum, mat) => sum + mat.QtyRequired, 0);
          const totalQtyIssued = isCompleted ? totalQtyRequired : 0;
          
          // Calculate completion percentages
          const materialsCompletionPct = isCompleted ? 100 : 0;
          const operationsCompletionPct = isCompleted ? 100 : 0;
          
          // Build the production order object with all available data
          const result = {
            id,
            MainProductionNbr: mainProductionNbr,
            MainOrderType: mainOrderType,
            MainOrderTypeDescription: mainOrderTypeDescription,
            MainStatus: mainStatus,
            MainDescription: mainDescription,
            MainInventoryID: mainInventoryID,
            MainInventoryClass: mainInventoryClass,
            MainQty: 1, // Default to 1 as quantity is not explicitly provided
            MainUOM: mainUOM,
            MainWarehouse: mainWarehouse,
            MainLocation: '',
            MainOrderDate: mainOrderDate,
            TotalMaterialCost: totalMaterialCost,
            Operations: operations,
            Materials: materials,
            OperationsCount: operations.length,
            MaterialsCount: materials.length,
            TotalQtyRequired: totalQtyRequired,
            TotalQtyIssued: totalQtyIssued,
            MaterialsCompletionPct: materialsCompletionPct,
            OperationsCompletionPct: operationsCompletionPct
          };
          
          // Validate result before returning
          if (!Array.isArray(result.Operations)) {
            console.error(`Operations is not an array for ${mainProductionNbr}`, result.Operations);
            result.Operations = [];
            result.OperationsCount = 0;
          }
          
          if (!Array.isArray(result.Materials)) {
            console.error(`Materials is not an array for ${mainProductionNbr}`, result.Materials);
            result.Materials = [];
            result.MaterialsCount = 0;
          }
          
          // Log the final object structure for debugging
          console.log("Final production order structure for", mainProductionNbr, ":", {
            id: result.id,
            OperationsCount: result.OperationsCount,
            MaterialsCount: result.MaterialsCount
          });
          
          return result;
        } catch (orderError) {
          console.error("Error parsing individual order:", orderError, order);
          return null;
        }
      }).filter(order => order !== null); // Filter out any orders that failed to parse
    } catch (error) {
      console.error("Error parsing Acumatica production orders:", error);
      return [];
    }
  }

  async getProductionFromIndexedDB() {
    return new Promise((resolve, reject) => {
      // Open without specifying version to use existing version
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database:", event.target.error);
        reject(new Error("Could not open production database"));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for reading, version:", db.version);
        
        // Check if the store exists
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          resolve([]);
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readonly");
        const store = transaction.objectStore(this.storeName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          const productionOrders = getAllRequest.result;
          console.log(`Retrieved ${productionOrders.length} production orders from IndexedDB`);
          resolve(productionOrders);
        };
        
        getAllRequest.onerror = (event) => {
          console.error("Error retrieving production orders:", event.target.error);
          reject(new Error("Failed to retrieve production orders from database"));
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error retrieving production orders:", event.target.error);
          db.close();
        };
      };
      
      request.onupgradeneeded = (event) => {
        // This shouldn't happen when opening an existing database without specifying version
        console.warn("Unexpected database upgrade needed during read operation");
        event.target.transaction.abort();
        reject(new Error("Database schema needs upgrade, please reload the page"));
      };
    });
  }

  async storeProductionInIndexedDB(productionOrders) {
    return new Promise((resolve, reject) => {
      try {
        // Open database
        const request = indexedDB.open(this.dbName);
        
        request.onerror = (event) => {
          console.error("Error opening database for storing:", event.target.error);
          reject(new Error("Could not open production database for storing"));
        };
        
        request.onsuccess = (event) => {
          const db = event.target.result;
          console.log("Successfully opened database for writing, version:", db.version);
          
          // Check if the store exists
          if (!db.objectStoreNames.contains(this.storeName)) {
            db.close();
            reject(new Error("Production store not found for storing"));
            return;
          }
          
          try {
            // Verify we have data to store
            console.log(`Preparing to store ${productionOrders.length} production orders`);
            
            // Debug log some of the orders
            if (productionOrders.length > 0) {
              const firstOrder = productionOrders[0];
              console.log("First order to store:", {
                id: firstOrder.id,
                MainProductionNbr: firstOrder.MainProductionNbr,
                OperationsCount: firstOrder.Operations ? firstOrder.Operations.length : 0,
                MaterialsCount: firstOrder.Materials ? firstOrder.Materials.length : 0
              });
              
              // Check if Operations and Materials are arrays
              console.log("Operations is array:", Array.isArray(firstOrder.Operations));
              console.log("Materials is array:", Array.isArray(firstOrder.Materials));
              
              // Deep clone each order to ensure all properties are properly stored
              const clonedOrders = productionOrders.map(order => {
                // Create a deep clone
                const clone = JSON.parse(JSON.stringify(order));
                
                // Ensure Operations and Materials are arrays
                if (!Array.isArray(clone.Operations)) {
                  console.warn(`Order ${clone.MainProductionNbr} has invalid Operations - fixing`);
                  clone.Operations = [];
                }
                
                if (!Array.isArray(clone.Materials)) {
                  console.warn(`Order ${clone.MainProductionNbr} has invalid Materials - fixing`);
                  clone.Materials = [];
                }
                
                // Update counts to match arrays
                clone.OperationsCount = clone.Operations.length;
                clone.MaterialsCount = clone.Materials.length;
                
                return clone;
              });
              
              // Start transaction
              const transaction = db.transaction([this.storeName], "readwrite");
              const store = transaction.objectStore(this.storeName);
              
              // Set up transaction event handlers
              transaction.oncomplete = () => {
                console.log("Transaction completed successfully");
                db.close();
                resolve(true);
              };
              
              transaction.onerror = (event) => {
                console.error("Transaction error:", event.target.error);
                db.close();
                reject(new Error("Failed to store production data: " + event.target.error.message));
              };
              
              // Clear existing data
              const clearRequest = store.clear();
              
              clearRequest.onsuccess = () => {
                console.log(`Cleared existing data. Storing ${clonedOrders.length} orders.`);
                
                // If no orders to store, we're done
                if (clonedOrders.length === 0) {
                  db.close();
                  resolve(true);
                  return;
                }
                
                // Counter for completed operations
                let completedCount = 0;
                const totalCount = clonedOrders.length;
                
                // Process each order
                clonedOrders.forEach((order) => {
                  try {
                    // Log order before storing
                    console.log(`Storing order ${order.MainProductionNbr} with ${order.OperationsCount} operations and ${order.MaterialsCount} materials`);
                    
                    const putRequest = store.put(order);
                    
                    putRequest.onsuccess = () => {
                      completedCount++;
                      if (completedCount === totalCount) {
                        console.log(`Successfully stored all ${completedCount} orders`);
                      }
                    };
                    
                    putRequest.onerror = (event) => {
                      console.error(`Error storing order ${order.MainProductionNbr}:`, event.target.error);
                      completedCount++;
                      event.stopPropagation(); // Prevent transaction abort
                      
                      if (completedCount === totalCount) {
                        console.log(`Completed storage operations with some errors`);
                      }
                    };
                  } catch (putError) {
                    console.error("Error in put operation:", putError);
                    completedCount++;
                    
                    if (completedCount === totalCount) {
                      console.log(`Completed storage operations with some errors`);
                    }
                  }
                });
              };
              
              clearRequest.onerror = (event) => {
                console.error("Error clearing store:", event.target.error);
                db.close();
                reject(new Error("Failed to clear existing data"));
              };
            } else {
              console.warn("No production orders to store");
              db.close();
              resolve(true);
            }
          } catch (transactionError) {
            console.error("Error setting up transaction:", transactionError);
            db.close();
            reject(new Error("Failed to set up database transaction"));
          }
        };
        
      } catch (error) {
        console.error("Unexpected error in storeProductionInIndexedDB:", error);
        reject(new Error("Unexpected error storing production data"));
      }
    });
  }

  async saveSettings(settings) {
    try {
      // Open database
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for saving settings:", event.target.error);
          reject(new Error("Could not open database for saving settings"));
        };
      });
      
      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.error("Settings store not found, cannot save settings");
        db.close();
        return;
      }
      
      const transaction = db.transaction([this.settingsStoreName], "readwrite");
      const store = transaction.objectStore(this.settingsStoreName);
      
      // Save production settings
      store.put({
        id: "productionSettings",
        ...settings
      });
      
      await new Promise((resolve, reject) => {
        transaction.oncomplete = () => {
          resolve();
        };
        transaction.onerror = (event) => {
          reject(event.target.error);
        };
      });
      
      db.close();
      return true;
    } catch (error) {
      console.error("Error saving settings:", error);
      return false;
    }
  }

  async loadSettings() {
    try {
      // Open database
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for loading settings:", event.target.error);
          reject(new Error("Could not open database for loading settings"));
        };
      });
      
      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.log("Settings store not found, using defaults");
        db.close();
        return null;
      }
      
      const transaction = db.transaction([this.settingsStoreName], "readonly");
      const store = transaction.objectStore(this.settingsStoreName);
      
      // Get production settings
      const productionSettings = await new Promise((resolve) => {
        const request = store.get("productionSettings");
        request.onsuccess = () => resolve(request.result);
        request.onerror = (event) => {
          console.error("Error reading production settings:", event.target.error);
          resolve(null);
        };
      });
      
      // Apply settings if they exist
      if (productionSettings) {
        if (productionSettings.itemsPerPage) {
          this.itemsPerPage = productionSettings.itemsPerPage;
        }
        
        if (productionSettings.sortField) {
          this.sortField = productionSettings.sortField;
        }
        
        if (productionSettings.sortDirection) {
          this.sortDirection = productionSettings.sortDirection;
        }
        
        if (productionSettings.lastSyncTime) {
          this.lastSyncTime = productionSettings.lastSyncTime;
        }
        
        if (productionSettings.dateRange) {
          try {
            this.dateRange = {
              start: new Date(productionSettings.dateRange.start),
              end: new Date(productionSettings.dateRange.end)
            };
          } catch (e) {
            console.error("Error parsing date range from settings:", e);
          }
        }
      }
      
      db.close();
      return productionSettings;
    } catch (error) {
      console.error("Error loading settings:", error);
      return null;
    }
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading production data...</p>
      </div>
    `;
  }

  renderContent() {
    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        ${this.renderDataSourceNotice()}
        <div class="flex flex-col md:flex-row justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">Production Orders</h2>
          
          <div class="flex flex-wrap items-center gap-2">
            <div class="relative">
              <input 
                type="text" 
                id="production-search" 
                placeholder="Search production orders..." 
                class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
                value="${this.searchTerm || ''}"
              >
              <button id="clear-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <select id="order-status-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
              <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Orders</option>
              <option value="planned" ${this.filterStatus === 'planned' ? 'selected' : ''}>Planned</option>
              <option value="in process" ${this.filterStatus === 'in process' ? 'selected' : ''}>In Process</option>
              <option value="completed" ${this.filterStatus === 'completed' ? 'selected' : ''}>Completed</option>
              <option value="canceled" ${this.filterStatus === 'canceled' ? 'selected' : ''}>Canceled</option>
            </select>
            
            <div class="flex gap-2">
              <!-- Production Orders Button -->
              <button id="production-button" class="p-2 bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 rounded-md flex items-center" title="Production Orders">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <span class="ml-1 text-sm font-medium">Production</span>
              </button>

              <!-- Date Range Button -->
              <button id="date-range-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Date Range">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </button>

              <!-- Refresh Button -->
              <button id="refresh-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Refresh Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>
              
              <!-- Export Button -->
              <button id="export-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Export Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
              </button>
              
              <!-- Settings Button -->
              <button id="settings-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Settings">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        <div id="production-table-container">
          <!-- Production Orders Table -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="MainProductionNbr">
                    Order # <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="MainInventoryID">
                    Item <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="MainDescription">
                    Description <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="MainQty">
                    Qty <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="MainStatus">
                    Status <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="MainOrderDate">
                    Order Date <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="MainOrderType">
                    Order Type <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${this.renderTableRows()}
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
            <div class="text-sm text-gray-500 dark:text-gray-400">
              Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredOrders.length)} to 
              ${Math.min(this.currentPage * this.itemsPerPage, this.filteredOrders.length)} of 
              ${this.filteredOrders.length} results
            </div>
            
            <div class="flex items-center space-x-1">
              <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-double-left"></i>
              </button>
              <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-left"></i>
              </button>
              
              <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
                ${this.currentPage} of ${this.totalPages}
              </span>
              
              <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-right"></i>
              </button>
              <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-double-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    this.setupEventListeners();
  }

  renderDataSourceNotice() {
    if (!this.dataSource || this.productionOrders.length === 0) {
      return `
        <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4 dark:bg-blue-900 dark:border-blue-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-blue-500 dark:text-blue-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-blue-800 dark:text-blue-200">
                No production data available. Connect to Acumatica and click the refresh button to load production orders.
              </p>
            </div>
          </div>
        </div>
      `;
    }
    
    if (this.dataSource === 'acumatica') {
      return `
        <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-4 dark:bg-green-900 dark:border-green-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-green-500 dark:text-green-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-green-700 dark:text-green-200">
                Displaying live data from Acumatica. Last synced: ${this.lastSyncTime || 'Unknown'}
              </p>
            </div>
          </div>
        </div>
      `;
    }
    
    if (this.dataSource === 'indexedDB') {
      // Removed the yellow offline data notification banner
      return '';
    }
    
    if (this.dataSource === 'sample') {
      return `
        <div class="bg-purple-50 border-l-4 border-purple-500 p-4 mb-4 dark:bg-purple-900 dark:border-purple-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-purple-500 dark:text-purple-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-purple-700 dark:text-purple-200">
                Displaying sample data. Connect to Acumatica for real production data.
              </p>
            </div>
          </div>
        </div>
      `;
    }
    
    return '';
  }

  renderTableRows() {
    if (this.filteredOrders.length === 0) {
      return `
        <tr>
          <td colspan="9" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
            No production orders found. Click the refresh button to load data.
          </td>
        </tr>
      `;
    }

    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = Math.min(start + this.itemsPerPage, this.filteredOrders.length);
    const displayedOrders = this.filteredOrders.slice(start, end);

    return displayedOrders.map(order => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400 font-medium">
          ${this.escapeHtml(order.MainProductionNbr || '')}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
          ${this.escapeHtml(order.MainInventoryID || '')}
        </td>
        <td class="px-3 py-4 text-sm text-gray-800 dark:text-gray-200">
          <div class="line-clamp-2">${this.escapeHtml(order.MainDescription || '')}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          ${parseFloat(order.MainQty || 0).toFixed(2)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-center">
          <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(order.MainStatus || '')}">
            ${this.escapeHtml(order.MainStatus || '')}
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatDate(order.MainOrderDate)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.escapeHtml(order.MainOrderType || '')}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button data-id="${order.id}" class="view-order text-blue-600 hover:text-blue-900 dark:hover:text-blue-400">
            <i class="fas fa-eye"></i>
          </button>
        </td>
      </tr>
    `).join('');
  }

  setupEventListeners() {
    // Add a small delay to ensure DOM is fully rendered
    setTimeout(() => {
      // Search input
      const searchInput = document.getElementById('production-search');
      if (searchInput) {
        searchInput.addEventListener('input', this.debounce(() => {
          this.searchTerm = searchInput.value.trim();
          this.currentPage = 1;
          this.applyFilters();
        }, 300));
      }

      // Clear search
      const clearSearchBtn = document.getElementById('clear-search');
      if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', () => {
          this.searchTerm = '';
          if (searchInput) searchInput.value = '';
          this.currentPage = 1;
          this.applyFilters();
        });
      }

      // Status filter
      const statusFilter = document.getElementById('order-status-filter');
      if (statusFilter) {
        statusFilter.addEventListener('change', () => {
          this.filterStatus = statusFilter.value;
          this.currentPage = 1;
          this.applyFilters();
        });
      }
      
      // Refresh button
      const refreshButton = document.getElementById('refresh-button');
      if (refreshButton) {
        refreshButton.addEventListener('click', () => {
          console.log('Refresh button clicked');
          this.loadData(true);
        });
      }

      // Export button
      const exportButton = document.getElementById('export-button');
      if (exportButton) {
        // Remove any existing event listeners first to prevent duplicates
        exportButton.replaceWith(exportButton.cloneNode(true));
        // Get the fresh reference after cloning
        const freshExportButton = document.getElementById('export-button');
        if (freshExportButton) {
          freshExportButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Export button clicked');
            this.exportProductionData();
          });
        }
      }

      // Settings button
      const settingsButton = document.getElementById('settings-button');
      if (settingsButton) {
        settingsButton.addEventListener('click', () => {
          console.log('Settings button clicked');
          this.showSettings();
        });
      }

      // Sort headers
      const sortHeaders = document.querySelectorAll('th[data-sort]');
      sortHeaders.forEach(header => {
        header.addEventListener('click', () => {
          const field = header.getAttribute('data-sort');
          if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
          } else {
            this.sortField = field;
            this.sortDirection = 'asc';
          }
          this.applyFilters();
        });
      });

      // Pagination event handlers
      const firstPageBtn = document.getElementById('first-page');
      if (firstPageBtn) {
        firstPageBtn.addEventListener('click', () => {
          console.log('First page button clicked');
          if (this.currentPage > 1) {
            this.currentPage = 1;
            this.render();
          }
        });
      }

      const prevPageBtn = document.getElementById('prev-page');
      if (prevPageBtn) {
        prevPageBtn.addEventListener('click', () => {
          console.log('Previous page button clicked');
          if (this.currentPage > 1) {
            this.currentPage--;
            this.render();
          }
        });
      }

      const nextPageBtn = document.getElementById('next-page');
      if (nextPageBtn) {
        nextPageBtn.addEventListener('click', () => {
          console.log('Next page button clicked');
          if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.render();
          }
        });
      }

      const lastPageBtn = document.getElementById('last-page');
      if (lastPageBtn) {
        lastPageBtn.addEventListener('click', () => {
          console.log('Last page button clicked');
          if (this.currentPage < this.totalPages) {
            this.currentPage = this.totalPages;
            this.render();
          }
        });
      }

      // View order buttons
      const viewButtons = document.querySelectorAll('.view-order');
      viewButtons.forEach(button => {
        button.addEventListener('click', (event) => {
          event.preventDefault(); // Prevent default action
          event.stopPropagation(); // Stop event bubbling
          console.log('View order button clicked');
          const orderId = button.getAttribute('data-id');
          this.viewOrderDetails(orderId);
        });
      });

      // Date Range button
      const dateRangeButton = document.getElementById('date-range-button');
      if (dateRangeButton) {
        dateRangeButton.addEventListener('click', () => {
          console.log('Date range button clicked');
          this.showDateRangePicker();
        });
      }
      
      console.log('All event listeners have been set up');
    }, 50); // Small delay to ensure DOM is ready
  }

  applyFilters() {
    // Filter by search term
    let filtered = this.productionOrders;
    
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(order => 
        (order.MainProductionNbr?.toLowerCase().includes(term)) ||
        (order.MainDescription?.toLowerCase().includes(term)) ||
        (order.MainInventoryID?.toLowerCase().includes(term))
      );
    }
    
    // Filter by status
    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(order => 
        order.MainStatus?.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }
    
    // Sort the data
    filtered.sort((a, b) => {
      let comparison = 0;
      const fieldA = a[this.sortField];
      const fieldB = b[this.sortField];

      // Handle null/undefined values
      const valA = fieldA ?? '';
      const valB = fieldB ?? '';

      switch (this.sortField) {
        case 'MainProductionNbr':
        case 'MainInventoryID':
        case 'MainDescription':
        case 'MainStatus':
        case 'MainOrderType':
          comparison = String(valA).localeCompare(String(valB));
          break;
        case 'MainQty':
        case 'MaterialsCompletionPct':
        case 'OperationsCompletionPct':
          comparison = parseFloat(valA) - parseFloat(valB);
          break;
        case 'MainOrderDate':
        case 'MainPromiseDate':
        case 'MainRequestDate':
          // Properly handle our custom date objects
          if (valA && valA.isAcumaticaDate && valB && valB.isAcumaticaDate) {
            // Compare by year, month, day directly
            if (valA.year !== valB.year) {
              comparison = valA.year - valB.year;
            } else if (valA.month !== valB.month) {
              comparison = valA.month - valB.month;
            } else {
              comparison = valA.day - valB.day;
            }
          } else if (valA instanceof Date && valB instanceof Date) {
            // Standard Date object comparison
            comparison = valA.getTime() - valB.getTime();
          } else if (valA && valB) {
            // Fallback to string comparison if we can't determine the format
            comparison = String(valA).localeCompare(String(valB));
          } else {
            // Handle cases where one value might be null/undefined
            comparison = valA ? 1 : (valB ? -1 : 0);
          }
          break;
        default:
          comparison = String(valA).localeCompare(String(valB));
      }

      return this.sortDirection === 'asc' ? comparison : -comparison;
    });
    
    this.filteredOrders = filtered;
    this.calculateTotalPages();
    this.render();
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredOrders.length / this.itemsPerPage));
    
    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  // Helper methods
  getStatusClass(status) {
    switch (status.toLowerCase()) {
      case 'planned':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'in process':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'canceled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  formatDate(dateObj) {
    if (!dateObj) return 'N/A';
    
    try {
      // Handle our special Acumatica date object format
      if (dateObj && typeof dateObj === 'object' && dateObj.isAcumaticaDate) {
        // If we have extracted year, month, day properties, use them directly
        if (dateObj.year && dateObj.month && dateObj.day) {
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return `${monthNames[dateObj.month-1]} ${dateObj.day}, ${dateObj.year}`;
        }
        
        // If not, try to extract from the raw value
        if (dateObj.rawValue && typeof dateObj.rawValue === 'string') {
          if (dateObj.rawValue.includes('T')) {
            const datePart = dateObj.rawValue.split('T')[0];
            const [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
            
            if (year && month && day) {
              const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
              return `${monthNames[month-1]} ${day}, ${year}`;
            }
          }
        }
      }
      
      // Legacy support for objects with original property from previous version
      if (dateObj && typeof dateObj === 'object' && dateObj.original) {
        if (typeof dateObj.original === 'string' && dateObj.original.includes('T')) {
          const datePart = dateObj.original.split('T')[0];
          const [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
          
          if (year && month && day) {
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            return `${monthNames[month-1]} ${day}, ${year}`;
          }
        }
      }
      
      // Handle string date format directly
      if (typeof dateObj === 'string') {
        if (dateObj.includes('T')) {
          // ISO format: "2025-04-29T00:00:00+00:00"
          const datePart = dateObj.split('T')[0];
          const [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
          
          if (year && month && day) {
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            return `${monthNames[month-1]} ${day}, ${year}`;
          }
        }
        
        // Last resort - use Date object but with UTC methods
        try {
          const date = new Date(dateObj);
          if (!isNaN(date.getTime())) {
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            
            // Use UTC methods to avoid timezone issues
            return `${monthNames[date.getUTCMonth()]} ${date.getUTCDate()}, ${date.getUTCFullYear()}`;
          }
        } catch {
          // Ignore parsing errors
        }
      }
      
      // Fallback for Date objects - use UTC methods to avoid timezone issues
      if (dateObj instanceof Date) {
        if (!isNaN(dateObj.getTime())) {
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return `${monthNames[dateObj.getUTCMonth()]} ${dateObj.getUTCDate()}, ${dateObj.getUTCFullYear()}`;
        }
      }
      
      // Last resort fallback
      return 'N/A';
    } catch (e) {
      console.error("Error formatting date:", e, dateObj);
      return 'N/A';
    }
  }

  escapeHtml(text) {
    if (!text) return '';
    
    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  debounce(func, wait) {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }

  showError(message) {
    const errorElement = document.createElement('div');
    errorElement.id = 'production-error-message';
    errorElement.className = 'mt-4 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
      <button class="mt-2 px-2 py-1 bg-red-200 dark:bg-red-800 rounded text-sm" id="dismiss-error">Dismiss</button>
    `;
    
    if (this.container) {
      this.container.appendChild(errorElement);
      
      // Add event listener to dismiss button
      const dismissButton = document.getElementById('dismiss-error');
      if (dismissButton) {
        dismissButton.addEventListener('click', () => {
          const errorMsg = document.getElementById('production-error-message');
          if (errorMsg) {
            errorMsg.remove();
          }
        });
      }
    } else {
      console.error("Container not available to show error:", message);
    }
  }

  async viewOrderDetails(orderId) {
    const order = this.productionOrders.find(o => o.id === orderId);
    if (!order) return;
    
    console.log("Displaying order details:", {
      id: order.id,
      MainProductionNbr: order.MainProductionNbr,
      OperationsCount: order.Operations ? order.Operations.length : 0,
      MaterialsCount: order.Materials ? order.Materials.length : 0
    });
    
    // Ensure Operations and Materials are arrays
    const operations = Array.isArray(order.Operations) ? order.Operations : [];
    const materials = Array.isArray(order.Materials) ? order.Materials : [];
    
    console.log("Operations sample:", operations.length > 0 ? operations[0] : "none");
    console.log("Materials sample:", materials.length > 0 ? materials[0] : "none");
    
    // Build the modal content
    const modalContent = `
      <div class="modal-content overflow-y-auto" style="max-height: 70vh;">
        <div class="p-6">
          <div class="flex flex-col md:flex-row justify-between mb-6">
            <h2 class="text-xl font-semibold mb-2">Production Order ${this.escapeHtml(order.MainProductionNbr)}</h2>
            <div class="flex items-center">
              <span class="px-2 py-1 text-sm font-semibold rounded-full ${this.getStatusClass(order.MainStatus)}">
                ${this.escapeHtml(order.MainStatus)}
              </span>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Order Details -->
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Order Information</h3>
              <div class="space-y-2">
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Production #:</span> 
                  <span>${this.escapeHtml(order.MainProductionNbr)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Order Type:</span> 
                  <span>${this.escapeHtml(order.MainOrderType)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Status:</span> 
                  <span>${this.escapeHtml(order.MainStatus)}</span>
                </p>
                <p class="text-sm">
                  <span class="font-medium">Description:</span><br> 
                  <span>${this.escapeHtml(order.MainDescription)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Item:</span> 
                  <span>${this.escapeHtml(order.MainInventoryID)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Quantity:</span> 
                  <span>${order.MainQty} ${this.escapeHtml(order.MainUOM)}</span>
                </p>
              </div>
            </div>

            <!-- Date and Location Details -->
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Production Details</h3>
              <div class="space-y-2">
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Order Date:</span> 
                  <span>${this.formatDate(order.MainOrderDate)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Warehouse:</span> 
                  <span>${this.escapeHtml(order.MainWarehouse)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Order Type:</span> 
                  <span>${this.escapeHtml(order.MainOrderTypeDescription || order.MainOrderType)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Item Class:</span> 
                  <span>${this.escapeHtml(order.MainInventoryClass || '')}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Material Cost:</span> 
                  <span>${this.formatCurrency(order.TotalMaterialCost || 0)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Completion:</span> 
                  <div class="w-32 bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: ${Math.min(100, order.OperationsCompletionPct)}%"></div>
                  </div>
                </p>
              </div>
            </div>
          </div>

          <!-- Operations -->
          <div class="mt-6">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Operations (${operations.length})</h3>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Op #</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Work Center</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Start Date</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  ${operations.length > 0 ? operations.map(op => `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">${this.escapeHtml(op.OperationNbr)}</td>
                      <td class="px-3 py-2 text-sm text-gray-800 dark:text-gray-200">${this.escapeHtml(op.OperationDescription)}</td>
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${this.escapeHtml(op.WorkCenter)}</td>
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${this.escapeHtml(op.Status || order.MainStatus)}</td>
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${this.formatDate(op.StartDate)}</td>
                    </tr>
                  `).join('') : `
                    <tr>
                      <td colspan="5" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
                        No operations associated with this production order
                      </td>
                    </tr>
                  `}
                </tbody>
              </table>
            </div>
          </div>

          <!-- Materials -->
          <div class="mt-6">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Materials (${materials.length})</h3>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Op #</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Item #</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">UOM</th>
                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Qty Required</th>
                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Unit Cost</th>
                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Total Cost</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  ${materials.length > 0 ? materials.map(mat => `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">${this.escapeHtml(mat.OperationNbr)}</td>
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">${this.escapeHtml(mat.InventoryID)}</td>
                      <td class="px-3 py-2 text-sm text-gray-800 dark:text-gray-200">${this.escapeHtml(mat.Description)}</td>
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${this.escapeHtml(mat.UOM)}</td>
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${mat.QtyRequired.toFixed(2)}</td>
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${this.formatCurrency(mat.UnitCost)}</td>
                      <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${this.formatCurrency(mat.UnitCost * mat.QtyRequired)}</td>
                    </tr>
                  `).join('') : `
                    <tr>
                      <td colspan="7" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
                        No materials associated with this production order
                      </td>
                    </tr>
                  `}
                </tbody>
                <tfoot class="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th colspan="4" class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400">Totals:</th>
                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-800 dark:text-gray-200">${order.TotalQtyRequired.toFixed(2)}</th>
                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400"></th>
                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-800 dark:text-gray-200">
                      ${this.formatCurrency(order.TotalMaterialCost || 0)}
                    </th>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          <!-- Buttons -->
          <div class="flex justify-end gap-2 mt-6">
            <button id="order-details-close-button" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
              Close
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Use our new modal helper
    const modalController = this.createModal('order-details-modal', modalContent, 'max-w-4xl');
    
    // Wait for modal to close
    await modalController.promise;
  }

  exportProductionData() {
    try {
      console.log("Starting export with", this.filteredOrders.length, "orders");
      
      if (!this.filteredOrders || this.filteredOrders.length === 0) {
        alert("No data available to export");
        return;
      }
      
      const fileName = `production_orders_${new Date().toISOString().slice(0, 10)}`;
      
      // Just use CSV export for reliability
      this.exportToCSV(fileName);
    } catch (error) {
      console.error('Error exporting data:', error);
      alert("Error exporting data: " + error.message);
    }
  }
  
  exportToCSV(fileName) {
    try {
      // Create CSV content
      let csvContent = 'data:text/csv;charset=utf-8,';
      
      // Add header row with BOM for Excel compatibility
      csvContent += '\uFEFF';
      csvContent += 'Production #,Order Type,Status,Item,Description,Quantity,UOM,Order Date,Warehouse,Operations Count,Materials Count,Completion %\n';
      
      console.log(`Exporting ${this.filteredOrders.length} production orders to CSV`);
      
      // Add each row of data
      this.filteredOrders.forEach((order, index) => {
        // Debug log every 10th order
        if (index % 10 === 0) {
          console.log(`Processing order ${index}:`, order.MainProductionNbr);
        }
        
        const row = [
          order.MainProductionNbr || '',
          order.MainOrderType || '',
          order.MainStatus || '',
          order.MainInventoryID || '',
          order.MainDescription || '',
          (order.MainQty != null) ? Number(order.MainQty).toFixed(2) : '0.00',
          order.MainUOM || '',
          this.formatDate(order.MainOrderDate) || '',
          order.MainWarehouse || '',
          order.OperationsCount || 0,
          order.MaterialsCount || 0,
          (order.OperationsCompletionPct != null) ? Number(order.OperationsCompletionPct).toFixed(1) + '%' : '0.0%'
        ].map(cell => {
          // Properly escape values for CSV
          if (cell === null || cell === undefined) {
            return '""';
          }
          // Escape quotes and wrap in quotes
          const cellStr = String(cell).replace(/"/g, '""');
          return `"${cellStr}"`;
        }).join(',');
        
        csvContent += row + '\n';
      });
      
      // Create and use a single download link
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', `${fileName}.csv`);
      document.body.appendChild(link);
      
      console.log("Triggering CSV download");
      link.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(link);
        console.log("Export completed and link removed");
      }, 100);
      
      alert("Production data exported successfully");
    } catch (error) {
      console.error("Error in CSV export:", error);
      alert("Error exporting to CSV: " + error.message);
    }
  }

  async showSettings() {
    // Build settings content
    const settingsHtml = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">Production Settings</h3>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Items per page</label>
          <select id="settings-items-per-page" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="5" ${this.itemsPerPage === 5 ? 'selected' : ''}>5</option>
            <option value="10" ${this.itemsPerPage === 10 ? 'selected' : ''}>10</option>
            <option value="25" ${this.itemsPerPage === 25 ? 'selected' : ''}>25</option>
            <option value="50" ${this.itemsPerPage === 50 ? 'selected' : ''}>50</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Default sort</label>
          <select id="settings-default-sort" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="MainProductionNbr" ${this.sortField === 'MainProductionNbr' ? 'selected' : ''}>Production #</option>
            <option value="MainInventoryID" ${this.sortField === 'MainInventoryID' ? 'selected' : ''}>Item</option>
            <option value="MainDescription" ${this.sortField === 'MainDescription' ? 'selected' : ''}>Description</option>
            <option value="MainQty" ${this.sortField === 'MainQty' ? 'selected' : ''}>Quantity</option>
            <option value="MainStatus" ${this.sortField === 'MainStatus' ? 'selected' : ''}>Status</option>
            <option value="MainOrderDate" ${this.sortField === 'MainOrderDate' ? 'selected' : ''}>Order Date</option>
            <option value="MainOrderType" ${this.sortField === 'MainOrderType' ? 'selected' : ''}>Order Type</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Sort direction</label>
          <div class="flex gap-4">
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="asc" ${this.sortDirection === 'asc' ? 'checked' : ''} class="mr-2">
              Ascending
            </label>
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="desc" ${this.sortDirection === 'desc' ? 'checked' : ''} class="mr-2">
              Descending
            </label>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-6">
          <button id="settings-cancel-button" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Cancel
          </button>
          <button id="settings-save-button" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            Save Changes
          </button>
        </div>
      </div>
    `;
    
    // Show modal using the controller and wait for result
    const modalController = this.createModal('settings-modal', settingsHtml, 'max-w-md');
    
    // We need to get form elements now while the modal is open
    const itemsPerPageSelect = modalController.modalContainer.querySelector('#settings-items-per-page');
    const defaultSortSelect = modalController.modalContainer.querySelector('#settings-default-sort');
    const sortDirectionRadios = modalController.modalContainer.querySelectorAll('input[name="settings-sort-direction"]');
    
    // Capture initial values
    let selectedItems = itemsPerPageSelect ? parseInt(itemsPerPageSelect.value) : this.itemsPerPage;
    let selectedSort = defaultSortSelect ? defaultSortSelect.value : this.sortField;
    let selectedDirection = this.sortDirection;
    
    // Add change listeners to capture values
    if (itemsPerPageSelect) {
      itemsPerPageSelect.addEventListener('change', () => {
        selectedItems = parseInt(itemsPerPageSelect.value);
      });
    }
    
    if (defaultSortSelect) {
      defaultSortSelect.addEventListener('change', () => {
        selectedSort = defaultSortSelect.value;
      });
    }
    
    sortDirectionRadios.forEach(radio => {
      radio.addEventListener('change', () => {
        if (radio.checked) {
          selectedDirection = radio.value;
        }
      });
    });
    
    // Wait for modal result
    const result = await modalController.promise;
    
    // Handle result based on action
    if (!result || result.action === 'cancel') {
      return; // User cancelled
    }
    
    if (result.action === 'save') {
      // Apply the captured values
      this.itemsPerPage = selectedItems;
      this.sortField = selectedSort;
      this.sortDirection = selectedDirection;
      
      // Save settings to IndexedDB
      await this.saveSettings({
        itemsPerPage: this.itemsPerPage,
        sortField: this.sortField,
        sortDirection: this.sortDirection
      });
      
      // Apply settings and re-render
      this.calculateTotalPages();
      this.applyFilters();
    }
  }

  formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      currencyDisplay: 'symbol'
    }).format(value);
  }

  async showDateRangePicker() {
    try {
      // Format dates for input fields
      const formatDateForInput = (date) => {
        if (!date) return '';
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };
      
      // Format current dates for the modal
      const initialStartDateValue = formatDateForInput(this.dateRange.start);
      const initialEndDateValue = formatDateForInput(this.dateRange.end);
      
      // Preset options
      const presets = [
        { label: 'Today', days: 0 },
        { label: 'Last 7 Days', days: 7 },
        { label: 'Last 30 Days', days: 30 },
        { label: 'Last 90 Days', days: 90 },
        { label: 'Last 180 Days', days: 180 },
        { label: 'Last Year', days: 365 },
        { label: 'All Time', days: 3650 } // ~10 years
      ];
      
      const presetsHtml = presets.map(preset => `
        <button type="button" class="date-preset px-3 py-1 text-sm rounded border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300" data-days="${preset.days}">
          ${preset.label}
        </button>
      `).join('');
      
      // Build the modal content
      const modalContent = `
        <div class="p-6">
          <h3 class="text-lg font-medium mb-3">Select Date Range</h3>
          <p class="mb-4 text-sm text-gray-600 dark:text-gray-400">
            Filter production orders based on order date within this range.
          </p>
          
          <div class="mb-4 flex flex-wrap gap-2">
            ${presetsHtml}
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label class="block text-sm font-medium mb-1">Start Date</label>
              <input 
                type="date" 
                id="date-range-start" 
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${initialStartDateValue}"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium mb-1">End Date</label>
              <input 
                type="date" 
                id="date-range-end" 
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${initialEndDateValue}"
              >
            </div>
          </div>
          
          <div class="flex justify-between">
            <div>
              <button id="date-range-clear-button" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md">
                Clear Filter
              </button>
            </div>
            <div class="flex gap-2">
              <button id="date-range-cancel-button" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
                Cancel
              </button>
              <button id="date-range-apply-button" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
                Apply Filter
              </button>
            </div>
          </div>
          
          <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <button id="date-range-fetch-button" class="w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Fetch Orders from Acumatica
            </button>
          </div>
        </div>
      `;
      
      // Create modal using our helper - now we get the modalController with modalContainer
      const modalController = this.createModal('date-range-modal', modalContent, 'max-w-md');
      
      // Get references to the date inputs directly from the modalContainer
      // This is safer than getElementById which might find elements elsewhere in the document
      const startDateInput = modalController.modalContainer.querySelector('#date-range-start');
      const endDateInput = modalController.modalContainer.querySelector('#date-range-end');
      
      // Store input values before the modal closes
      let startDateValue, endDateValue;
      
      // Handle preset buttons - we'll only do this if we've found the inputs
      if (startDateInput && endDateInput) {
        const presetButtons = modalController.modalContainer.querySelectorAll('.date-preset');
        
        presetButtons.forEach(button => {
          button.addEventListener('click', () => {
            const days = parseInt(button.dataset.days);
            const end = new Date();
            const start = new Date();
            
            if (days === 0) {
              // Today only
              start.setHours(0, 0, 0, 0);
            } else {
              // Go back X days
              start.setDate(end.getDate() - days);
              start.setHours(0, 0, 0, 0);
            }
            
            // Update the input fields
            startDateInput.value = formatDateForInput(start);
            endDateInput.value = formatDateForInput(end);
            
            // Also store the values in our variables
            startDateValue = startDateInput.value;
            endDateValue = endDateInput.value;
          });
        });
        
        // Add input event listeners to capture the values as they change
        startDateInput.addEventListener('change', () => {
          startDateValue = startDateInput.value;
        });
        
        endDateInput.addEventListener('change', () => {
          endDateValue = endDateInput.value;
        });
        
        // Initialize with current values
        startDateValue = startDateInput.value;
        endDateValue = endDateInput.value;
      } else {
        console.error("Date inputs not found in modal container");
        return; // Exit early if inputs aren't found
      }
      
      // Wait for modal result
      const result = await modalController.promise;
      console.log('Modal result:', result);
      
      if (!result || result.action === 'cancel') {
        return; // User cancelled
      }
      
      if (result.action === 'clear') {
        // Reset to default date range
        const now = new Date();
        const threeMonthsAgo = new Date();
        threeMonthsAgo.setMonth(now.getMonth() - 3);
        
        this.dateRange = {
          start: threeMonthsAgo,
          end: now
        };
        
        return;
      }
      
      // For both apply and fetch actions:
      // Use the stored input values rather than trying to access the inputs again
      // (which won't work since the modal is now closed)
      const startDate = startDateValue ? new Date(startDateValue) : null;
      const endDate = endDateValue ? new Date(endDateValue) : null;
      
      // Validate dates
      if (!startDate || !endDate) {
        this.showError("Please select both start and end dates");
        return;
      }
      
      if (startDate > endDate) {
        this.showError("Start date cannot be after end date");
        return;
      }
      
      // Update date range
      this.dateRange = {
        start: startDate,
        end: endDate
      };
      
      if (result.action === 'fetch') {
        // Show loading state
        this.isLoading = true;
        this.render();
        
        // Get the connection status and fetch data
        const connectionStatus = connectionManager.getConnectionStatus();
        if (!connectionStatus.acumatica.isConnected) {
          this.isLoading = false;
          this.render();
          this.showError('Not connected to Acumatica. Please connect and try again.');
          return;
        }
        
        try {
          // Fetch data from Acumatica with the specified date range
          await this.loadData(true);
        } catch (error) {
          console.error('Error fetching data from Acumatica:', error);
          this.isLoading = false;
          this.render();
          this.showError(`Failed to fetch data from Acumatica: ${error.message}`);
        }
      } else if (result.action === 'apply') {
        // Just apply the filter to existing data
        this.applyFilters();
      }
    } catch (error) {
      // Handle any unexpected errors
      console.error("Error in showDateRangePicker:", error);
      this.showError(`Error with date picker: ${error.message}`);
    }
  }

  // Additional helper method for date formatting in button
  formatDateShort(date) {
    if (!date) return '';
    
    try {
      // Handle our special Acumatica date object format
      if (date && typeof date === 'object' && date.isAcumaticaDate) {
        // If we have extracted year, month, day properties, use them directly
        if (date.year && date.month && date.day) {
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return `${monthNames[date.month-1]} ${date.day}`;
        }
        
        // If not, try to extract from the raw value
        if (date.rawValue && typeof date.rawValue === 'string') {
          if (date.rawValue.includes('T')) {
            const datePart = date.rawValue.split('T')[0];
            const [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
            
            if (month && day) {
              const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
              return `${monthNames[month-1]} ${day}`;
            }
          }
        }
      }
      
      // Legacy support for objects with original property from previous version
      if (date && typeof date === 'object' && date.original) {
        if (typeof date.original === 'string' && date.original.includes('T')) {
          const datePart = date.original.split('T')[0];
          const [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
          
          if (month && day) {
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            return `${monthNames[month-1]} ${day}`;
          }
        }
      }
      
      // Handle string date format directly
      if (typeof date === 'string' && date.includes('T')) {
        const datePart = date.split('T')[0];
        const [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
        
        if (month && day) {
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return `${monthNames[month-1]} ${day}`;
        }
      }
      
      // Fallback for Date objects - use UTC
      if (date instanceof Date) {
        if (!isNaN(date.getTime())) {
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return `${monthNames[date.getUTCMonth()]} ${date.getUTCDate()}`;
        }
      }
      
      // Last resort - try parsing as Date with UTC
      try {
        const dateObj = new Date(date);
        if (!isNaN(dateObj.getTime())) {
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return `${monthNames[dateObj.getUTCMonth()]} ${dateObj.getUTCDate()}`;
        }
      } catch {
        // Ignore parsing errors
      }
      
      return '';
    } catch (e) {
      console.error("Error formatting short date:", e, date);
      return '';
    }
  }

  // Generic helper method to create modals with proper event handling
  createModal(modalId, modalContent, width = 'max-w-4xl') {
    // Remove any existing modal with the same ID
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
      existingModal.remove();
    }
    
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modalOverlay.id = modalId;
    
    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.className = `bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full ${width} h-auto max-h-[80vh]`;
    modalContainer.innerHTML = modalContent;
    
    // Append to DOM
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Create object to hold our result data and controller functions
    const modalController = {
      modalContainer,
      modalOverlay,
      result: null,
      // This will be called when the modal is closed
      setResult: function(result) {
        this.result = result;
      }
    };
    
    // Create a promise that resolves when the modal is closed
    const modalPromise = new Promise((resolve) => {
      // Function to close the modal
      const closeModal = (result) => {
        const modal = document.getElementById(modalId);
        if (modal && modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
        
        // Remove global event listener
        document.removeEventListener('keydown', handleEscKey);
        
        // Save result and resolve promise
        modalController.setResult(result);
        resolve(result);
      };
      
      // Event handler for Escape key
      const handleEscKey = (event) => {
        if (event.key === 'Escape') {
          closeModal({action: 'cancel'});
        }
      };
      
      // Add global escape key handler
      document.addEventListener('keydown', handleEscKey);
      
      // Click outside to close
      modalOverlay.addEventListener('click', (event) => {
        if (event.target === modalOverlay) {
          closeModal({action: 'cancel'});
        }
      });
      
      // Find and attach event listeners to all buttons in the modal
      // Button IDs should follow pattern: {modalId}-{action}-button
      // e.g., settings-save-button, settings-cancel-button
      const buttons = modalContainer.querySelectorAll('button');
      buttons.forEach(button => {
        button.addEventListener('click', (event) => {
          event.preventDefault();
          event.stopPropagation();
          
          // Extract action from button ID if it follows the pattern
          let action = 'close';
          const idParts = button.id.split('-');
          if (idParts.length >= 2) {
            action = idParts[idParts.length - 2]; // Second to last part is the action
          }
          
          // Get any data attributes from the button
          const data = {};
          Object.keys(button.dataset).forEach(key => {
            data[key] = button.dataset[key];
          });
          
          // Close modal with result
          closeModal({
            action: action,
            button: button.id,
            data: data
          });
        });
      });
      
      // Add closeModal method to controller
      modalController.closeModal = closeModal;
    });
    
    // Attach the promise to the controller
    modalController.promise = modalPromise;
    
    // Return the controller with container and promise
    return modalController;
  }
} 