<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Price Tracker Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Simple Price Tracker Test</h1>

        <!-- Test Controls -->
        <div class="mb-6 p-4 bg-white rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4">Test Controls</h2>
            <div class="flex gap-4 mb-4">
                <button onclick="testChartToggle('NC7WZ16P6X', 'Newark')" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                    Test Chart Toggle (NC7WZ16P6X - Newark)
                </button>
                <button onclick="testChartToggle('FREIGHT', 'Swagelok Calgary')" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    Test Chart Toggle (FREIGHT - Swagelok Calgary)
                </button>
                <button onclick="debugComponent()" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                    Debug Component
                </button>
                <button onclick="forceRender()" class="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700">
                    Force Render
                </button>
                <button onclick="checkApexCharts()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                    Check ApexCharts
                </button>
            </div>
            <div id="test-output" class="text-sm text-gray-600 bg-gray-50 p-2 rounded min-h-[100px] font-mono"></div>
        </div>

        <!-- Container for the Price Tracker component -->
        <div id="price-tracker-container" class="bg-white rounded-lg shadow-lg p-6">
            <div class="text-center text-gray-500">Loading...</div>
        </div>
    </div>

    <script type="module">
        import { PriceTrackerComponent } from './kpi/purchasing/price_tracker.js';
        
        // Initialize the component
        const container = document.getElementById('price-tracker-container');
        const priceTracker = new PriceTrackerComponent(container);
        
        // Initialize the component
        priceTracker.init().then(() => {
            console.log('Price tracker initialized successfully');
            logToOutput('Price tracker initialized successfully');
        }).catch(error => {
            console.error('Error initializing price tracker:', error);
            logToOutput('Error initializing price tracker: ' + error.message);
        });
        
        // Make it globally available for debugging
        window.priceTracker = priceTracker;
        
        // Test functions
        
        window.testChartToggle = function(inventoryId, vendorName) {
            console.log('Testing chart toggle:', inventoryId, vendorName);
            logToOutput(`Testing chart toggle: ${inventoryId} - ${vendorName}`);
            if (priceTracker && priceTracker.testChartToggle) {
                priceTracker.testChartToggle(inventoryId, vendorName);
            } else {
                logToOutput('ERROR: priceTracker.testChartToggle not available');
            }
        };
        
        window.debugComponent = function() {
            console.log('Debugging component...');
            logToOutput('Debugging component...');
            if (priceTracker && priceTracker.debugButtons) {
                priceTracker.debugButtons();
            } else {
                logToOutput('ERROR: priceTracker.debugButtons not available');
            }
        };

        window.forceRender = function() {
            console.log('Force rendering...');
            logToOutput('Force rendering...');
            if (priceTracker && priceTracker.forceRender) {
                priceTracker.forceRender();
            } else {
                logToOutput('ERROR: priceTracker.forceRender not available');
            }
        };

        window.checkApexCharts = function() {
            console.log('Checking ApexCharts...');
            logToOutput('Checking ApexCharts...');
            logToOutput('ApexCharts available: ' + (typeof ApexCharts !== 'undefined'));
            if (typeof ApexCharts !== 'undefined') {
                logToOutput('ApexCharts version: ' + (ApexCharts.version || 'unknown'));
            }
        };
        
        function logToOutput(message) {
            const output = document.getElementById('test-output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        // Log console messages to output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToOutput('LOG: ' + args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToOutput('ERROR: ' + args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToOutput('WARN: ' + args.join(' '));
        };
    </script>
</body>
</html>
